import { ref, watch, onBeforeUnmount } from 'vue';
import { renderMarkdown, renderMarkdownLite } from '../../utils/MarkdownRenderer.js';

export default {
    name: 'TypewriterMarkdown',
    props: {
        content: { type: String, required: true },
        renderedContent: { type: String, default: '' },
        isStreaming: { type: Boolean, default: false },
        // 速度配置
        typewriterBaseCps: { type: Number, default: 24 },
        typewriterFinishCps: { type: Number, default: 240 },
        // 动态速度调节配置
        typewriterTargetSeconds: { type: Number, default: 5 },
        typewriterMaxDynamicCps: { type: Number, default: 240 },
        // 容器类
        containerClass: { type: String, default: 'markdown-content min-w-0' }
    },
    setup(props) {
        const renderedHtml = ref('');

        // 动画状态
        const targetContent = ref('');
        const visibleCount = ref(0);
        const isAnimating = ref(false);
        let rafId = null;
        let lastTs = 0;
        // 使用浮点累积以避免“每帧至少一个字符”的现象，确保按cps平滑推进
        let progressCount = 0;

        // 根据内容长度动态计算批次大小
        const getBatchSize = (totalLength) => {
            if (totalLength <= 100) return 1;      // 短内容：逐字显示
            if (totalLength <= 500) return 3;      // 中等内容：3字符一批
            if (totalLength <= 1000) return 8;     // 较长内容：8字符一批
            if (totalLength <= 2000) return 15;    // 长内容：15字符一批
            return Math.min(25, Math.max(15, Math.floor(totalLength / 100))); // 超长内容：动态调整，最多25字符一批
        };

        const getCps = (total, forceFast) => {
            const base = typeof props.typewriterBaseCps === 'number' ? props.typewriterBaseCps : 24;
            const fast = typeof props.typewriterFinishCps === 'number' ? props.typewriterFinishCps : Math.max(base * 3, base + 60);
            const maxDyn = typeof props.typewriterMaxDynamicCps === 'number' ? props.typewriterMaxDynamicCps : Math.max(fast, 240);
            // 完成阶段或非流式阶段：使用快速度
            if (forceFast || !props.isStreaming) return Math.max(fast, base);
            // 流式阶段：根据待展示 backlog 动态调节，目标在 targetSeconds 内完成
            const backlog = Math.max(0, (typeof total === 'number' ? total : 0) - visibleCount.value);
            const targetSec = (typeof props.typewriterTargetSeconds === 'number' && props.typewriterTargetSeconds > 0) ? props.typewriterTargetSeconds : 5;
            if (backlog <= 0) return base;
            const desired = backlog / Math.max(0.1, targetSec);
            return Math.max(base, Math.min(maxDyn, desired));
        };

        const stopAnimation = () => {
            if (rafId) cancelAnimationFrame(rafId);
            rafId = null;
            isAnimating.value = false;
        };

        const startAnimation = (forceFast = false) => {
            if (rafId) cancelAnimationFrame(rafId);
            isAnimating.value = true;
            lastTs = 0;
            // 保持已展示的字符数量，重置累积器为当前批次索引
            const currentBatchSize = getBatchSize(targetContent.value?.length || 0);
            progressCount = currentBatchSize > 0 ? Math.floor(visibleCount.value / currentBatchSize) : 0;

            // 如果目标内容为空，直接清空渲染内容
            if (!targetContent.value) {
                renderedHtml.value = '';
                isAnimating.value = false;
                return;
            }

            const step = (ts) => {
                if (!lastTs) lastTs = ts;
                const dt = (ts - lastTs) / 1000;
                lastTs = ts;

                const fullText = targetContent.value || '';
                const total = fullText.length;

                if (visibleCount.value < total) {
                    const cps = getCps(total, forceFast);
                    const batchSize = getBatchSize(total);

                    // 计算基于批次的进度增量
                    const batchesPerSecond = cps / batchSize;
                    progressCount += batchesPerSecond * dt;

                    // 计算下一个批次的结束位置
                    const nextBatchIndex = Math.floor(progressCount);
                    const nextCount = Math.min(total, (nextBatchIndex + 1) * batchSize);

                    if (nextCount > visibleCount.value) {
                        visibleCount.value = nextCount;
                        const partial = fullText.slice(0, visibleCount.value);
                        try {
                            // 流式阶段用轻量渲染，完成后再做完整高亮
                            renderedHtml.value = props.isStreaming ? renderMarkdownLite(partial) : renderMarkdown(partial);
                        } catch (e) {
                            renderedHtml.value = (partial || '').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br>');
                        }
                    }

                    rafId = requestAnimationFrame(step);
                } else {
                    // 动画完成：确保最终渲染是正确的
                    isAnimating.value = false;
                    rafId = null;

                    // 确保最终渲染内容与目标内容一致
                    if (!props.isStreaming && props.renderedContent) {
                        renderedHtml.value = props.renderedContent;
                    } else if (!props.isStreaming) {
                        renderedHtml.value = renderMarkdown(fullText);
                    } else {
                        // 流式状态下，确保显示完整的当前内容
                        try {
                            renderedHtml.value = renderMarkdownLite(fullText);
                        } catch (e) {
                            renderedHtml.value = (fullText || '').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br>');
                        }
                    }
                }
            };

            rafId = requestAnimationFrame(step);
        };

        const sync = () => {
            const contentStr = typeof props.content === 'string' ? props.content : (props.content ? JSON.stringify(props.content) : '');
            targetContent.value = contentStr;

            if (!props.isStreaming) {
                stopAnimation();
                visibleCount.value = contentStr.length;
                const batchSize = getBatchSize(contentStr.length);
                progressCount = batchSize > 0 ? Math.floor(contentStr.length / batchSize) : 0;
                renderedHtml.value = props.renderedContent || renderMarkdown(contentStr);
                return;
            }

            // 流式：处理内容变化
            if (visibleCount.value > contentStr.length) {
                // 新内容比当前显示的短，需要立即重新渲染较短的内容
                visibleCount.value = contentStr.length;
                const batchSize = getBatchSize(contentStr.length);
                progressCount = batchSize > 0 ? Math.floor(contentStr.length / batchSize) : 0;

                // 立即渲染新的较短内容，而不是等待动画循环
                if (contentStr.length === 0) {
                    // 内容为空时直接清空
                    renderedHtml.value = '';
                } else {
                    try {
                        renderedHtml.value = props.isStreaming ? renderMarkdownLite(contentStr) : renderMarkdown(contentStr);
                    } catch (e) {
                        renderedHtml.value = (contentStr || '').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br>');
                    }
                }
            }

            // 无论内容长短，都启动动画以处理可能的后续更新
            startAnimation(false);
        };

        watch(() => props.content, () => sync(), { immediate: true });
        watch(() => props.renderedContent, () => { if (!props.isStreaming) sync(); });
        watch(() => props.isStreaming, (nowStreaming) => {
            if (!nowStreaming) {
                if (targetContent.value) startAnimation(true); else stopAnimation();
            }
        });

        onBeforeUnmount(() => stopAnimation());

        return { renderedHtml };
    },
    template: `
        <div :data-skip-hljs="isStreaming ? 'true' : null" :class="containerClass" v-html="renderedHtml"></div>
    `
};
